import React, { useState, useEffect, useCallback } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Container, Typography, Box, Paper, Snackbar, Alert, CircularProgress, Button, CssBaseline, TextField, IconButton, Collapse } from '@mui/material';
import BarChartIcon from '@mui/icons-material/BarChart';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import RefreshIcon from '@mui/icons-material/Refresh';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import './App.css';
import { phrases, phraseCollectionConfig } from './phrases';

// Import components
import TestComponent from './TestComponent';
import ConsentPage from './components/ConsentPage';
import DemographicForm from './components/DemographicForm';
import CategorySelector from './components/CategorySelector';
import PhraseDisplay from './components/PhraseDisplay';
import VideoRecorder from './components/VideoRecorder';
import PhraseNavigation from './components/PhraseNavigation';
import RecordingProgress from './components/RecordingProgress';
import CollectionTracker from './components/CollectionTracker';
import PhraseSelector from './components/PhraseSelector';
import NavigationMenu from './components/NavigationMenu';
import TrainingVideoPage from './components/TrainingVideoPage';
import ReceiptGenerator from './components/ReceiptGenerator';
import UserProfile from './components/UserProfile';
import S3ProgressDisplay from './components/S3ProgressDisplay';
import S3ProgressPreloader from './components/S3ProgressPreloader';
import MouthDetectionTest from './components/MouthDetectionTest';
// EnvTest removed

// Import services
import { uploadVideoToS3 } from './services/awsStorage';
import videoStore from './services/videoStorage';
import referenceNumberService from './services/referenceNumberService';
import {
  getSelectedPhrases,
  saveSelectedPhrases,
  hasSelectedPhrases,
  clearSelectedPhrases,
  getCompletionPercentage,
  getTotalRecordings,
  getMaxPhrasesPerVolunteer
} from './services/phraseRotationService';
import { useProgressTracking } from './hooks/useProgressTracking';

const App = () => {
  const [hasConsent, setHasConsent] = useState(false);
  const [demographicsCompleted, setDemographicsCompleted] = useState(false);
  const [demographicInfo, setDemographicInfo] = useState(null);
  const [trainingVideoCompleted, setTrainingVideoCompleted] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [recordingsCount, setRecordingsCount] = useState({});
  const [currentRecordingNumber, setCurrentRecordingNumber] = useState(0);

  // Additional state variables needed for debugging (moved up to prevent hoisting issues)
  const [showCollectionTracker, setShowCollectionTracker] = useState(false);
  const [sessionRecordingsCount, setSessionRecordingsCount] = useState(0);
  const [selectedPhrases, setSelectedPhrases] = useState(null);
  const [phrasesSelected, setPhrasesSelected] = useState(false);
  const [currentStep, setCurrentStep] = useState('demographics'); // Track current navigation step - start with demographics instead of home
  const [showCompletionPrompt, setShowCompletionPrompt] = useState(false); // Show "Time for more?" prompt
  const [showReceipt, setShowReceipt] = useState(false); // Show receipt for user consent withdrawal
  const [showProfile, setShowProfile] = useState(false); // Toggle profile view
  const [sessionStats, setSessionStats] = useState({
    startTime: new Date(),
    recentRecordings: []
  });
  const [currentSessionReference, setCurrentSessionReference] = useState('');
  const [showProgress, setShowProgress] = useState(false);
  const [showReceiptGenerator, setShowReceiptGenerator] = useState(false);
  const [showDemographics, setShowDemographics] = useState(false);
  const [showPhraseSelector, setShowPhraseSelector] = useState(false);
  const [showRecording, setShowRecording] = useState(false);
  const [showTrainingVideo, setShowTrainingVideo] = useState(false);

  // Real-time progress tracking from S3
  const { refreshProgress } = useProgressTracking({
    autoRefresh: true,
    refreshInterval: 60 * 60 * 1000 // 1 hour (optimised for performance)
  });

  // Debug currentRecordingNumber changes
  useEffect(() => {
    console.log('📊 App.js: currentRecordingNumber changed to:', currentRecordingNumber);
  }, [currentRecordingNumber]);

  // Debug recordingsCount changes
  useEffect(() => {
    console.log('📊 App.js: recordingsCount changed to:', recordingsCount);
  }, [recordingsCount]);

  // Debug showCompletionPrompt changes
  useEffect(() => {
    console.log('🏁 App.js: showCompletionPrompt changed to:', showCompletionPrompt);
    if (showCompletionPrompt) {
      console.log('🏁 ===== COMPLETION PAGE TRIGGERED! =====');
      console.log('  🏁 CRITICAL: This should only happen after ALL phrases are completed');
      console.log('  🏁 Current state when completion triggered:');
      console.log('    currentPhraseIndex:', currentPhraseIndex);
      console.log('    selectedPhrases length:', selectedPhrases?.length);
      console.log('    currentRecordingNumber:', currentRecordingNumber);
      console.log('    recordingsCount:', recordingsCount);
      console.log('    RECORDINGS_PER_PHRASE:', RECORDINGS_PER_PHRASE);
      console.log('  🏁 This indicates a bug if triggered after only 1 recording');
      console.trace('🏁 Stack trace for completion trigger:');
    }
  }, [showCompletionPrompt, currentPhraseIndex, selectedPhrases, currentRecordingNumber, recordingsCount]);

  // Comprehensive state debugging
  const logAppState = (context) => {
    console.log(`📊 === APP STATE DEBUG (${context}) ===`);
    console.log('  currentPhraseIndex:', currentPhraseIndex);
    console.log('  currentRecordingNumber:', currentRecordingNumber);
    console.log('  recordingsCount:', recordingsCount);
    console.log('  selectedPhrases length:', selectedPhrases?.length);
    console.log('  showCompletionPrompt:', showCompletionPrompt);
    console.log('  uploading:', uploading);
    console.log('  currentPhrase:', currentPhrase);
    console.log('  selectedCategory:', selectedCategory);
  };

  // Test function for debugging progress bar (accessible from browser console)
  window.testProgressBar = () => {
    console.log('🧪 Testing progress bar with sample data');
    const testData = {
      'Basic Needs:I need water': 2,
      'Basic Needs:I need food': 3,
      'Health:I am in pain': 1,
      'Communication:Call my family': 3
    };
    setRecordingsCount(testData);
    console.log('🧪 Test data set:', testData);
  };

  // Test function for debugging completion page (accessible from browser console)
  window.testCompletionPage = () => {
    console.log('🧪 Testing completion page display');
    // Set up mock data for completion page
    setSessionRecordingsCount(9);
    setRecordingsCount({
      'Basic Needs:I need water': 3,
      'Basic Needs:I need food': 3,
      'Health:I am in pain': 3
    });
    setCurrentSessionReference('ICU-TEST123ABC');
    setShowCompletionPrompt(true);
    setCurrentStep('recording');
    console.log('🧪 Completion page should now be visible');
  };

  // Handle early exit completion (dedicated callback for VideoRecorder)
  const handleEarlyExitCompletion = () => {
    console.log('🚪 === EARLY EXIT COMPLETION TRIGGERED ===');
    console.log('  🚪 SETTING showCompletionPrompt = true (early exit)');
    console.log('  🚪 Current state when early exit triggered:');
    console.log('    currentPhraseIndex:', currentPhraseIndex);
    console.log('    currentRecordingNumber:', currentRecordingNumber);
    console.log('    recordingsCount:', recordingsCount);
    console.trace('🚪 Stack trace for early exit trigger:');
    setShowCompletionPrompt(true);
    setCurrentStep('recording');
  };

  // Global function to trigger early exit completion (fallback)
  window.triggerEarlyExitCompletion = () => {
    console.log('🚪 Early exit completion triggered via global function');
    handleEarlyExitCompletion();
  };

  // Global debug function to test auto-advancement
  window.debugAutoAdvancement = () => {
    console.log('🔧 DEBUG: Testing auto-advancement manually');
    console.log('Current state:', {
      currentPhraseIndex,
      currentRecordingNumber,
      selectedPhrases: selectedPhrases?.length,
      recordingsCount
    });
    handleNextPhrase();
  };

  // Global debug function to force recording count to 3 for testing
  window.debugForceRecordingCount = () => {
    console.log('🔧 DEBUG: Forcing recording count to 3 for current phrase');
    if (selectedPhrases && selectedPhrases[currentPhraseIndex]) {
      const currentPhraseObj = selectedPhrases[currentPhraseIndex];
      const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
      console.log('Setting', phraseKey, 'to 3 recordings');

      setRecordingsCount(prev => {
        const newCount = { ...prev };
        newCount[phraseKey] = 3;
        localStorage.setItem('icuAppRecordingsCount', JSON.stringify(newCount));
        return newCount;
      });

      setCurrentRecordingNumber(3);
      console.log('Forced recording count. Now call handleNextPhrase manually.');
    }
  };

  // Global debug function to check current state
  window.debugCurrentState = () => {
    console.log('🔍 DEBUG: Current application state');
    console.log('selectedPhrases:', selectedPhrases);
    console.log('currentPhraseIndex:', currentPhraseIndex);
    console.log('currentRecordingNumber:', currentRecordingNumber);
    console.log('recordingsCount:', recordingsCount);
    console.log('currentPhrase:', currentPhrase);
    console.log('selectedCategory:', selectedCategory);
    console.log('Is last phrase?', currentPhraseIndex >= (selectedPhrases?.length - 1));
    console.log('localStorage recordingsCount:', JSON.parse(localStorage.getItem('icuAppRecordingsCount') || '{}'));

    // Check current phrase key
    if (selectedPhrases && selectedPhrases[currentPhraseIndex]) {
      const currentPhraseObj = selectedPhrases[currentPhraseIndex];
      const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
      console.log('Current phrase key:', phraseKey);
      console.log('Current phrase recordings:', recordingsCount[phraseKey] || 0);
    }
  };




  const RECORDINGS_PER_PHRASE = phraseCollectionConfig.recordingsPerPhrase;
  const COLLECTION_GOAL = phraseCollectionConfig.collectionGoal;
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isLaptop = !isMobile;

  // Debug configuration values
  console.log('📋 App.js Configuration:');
  console.log('  RECORDINGS_PER_PHRASE:', RECORDINGS_PER_PHRASE);
  console.log('  COLLECTION_GOAL:', COLLECTION_GOAL);
  console.log('  phraseCollectionConfig:', phraseCollectionConfig);
  console.log('  🔧 Auto-advancement fix applied - useCallback dependencies resolved');

  // Auto-advancement effect - watches for recording count changes
  useEffect(() => {
    if (!selectedPhrases || selectedPhrases.length === 0 || currentPhraseIndex < 0) {
      return;
    }

    const currentPhraseObj = selectedPhrases[currentPhraseIndex];
    if (!currentPhraseObj) {
      return;
    }

    const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
    const currentCount = recordingsCount[phraseKey] || 0;

    console.log('🔄 AUTO-ADVANCEMENT EFFECT TRIGGERED:');
    console.log('  currentPhraseObj:', currentPhraseObj);
    console.log('  phraseKey:', phraseKey);
    console.log('  currentCount:', currentCount);
    console.log('  RECORDINGS_PER_PHRASE:', RECORDINGS_PER_PHRASE);
    console.log('  Should advance?', currentCount >= RECORDINGS_PER_PHRASE);

    if (currentCount >= RECORDINGS_PER_PHRASE) {
      console.log('🎯 EFFECT: Phrase completion detected, triggering advancement');

      // Use a small delay to ensure state is fully updated
      setTimeout(() => {
        console.log('🚀 EFFECT: Executing handleNextPhrase');
        handleNextPhrase();
      }, 50);
    }
  }, [recordingsCount, currentPhraseIndex, selectedPhrases, RECORDINGS_PER_PHRASE, handleNextPhrase]);
  
  // Check for direct access URL parameter and load localStorage data
  useEffect(() => {
    // Production deployment reset - clear all data for fresh start in production
    const isProduction = process.env.NODE_ENV === 'production';
    const hasDeploymentReset = localStorage.getItem('icuAppDeploymentReset');

    if (isProduction && !hasDeploymentReset) {
      console.log('🚀 Production deployment detected - clearing all localStorage for fresh start');
      try {
        // Clear all ICU app related localStorage data
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('icuApp')) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));

        // Set deployment reset flag
        localStorage.setItem('icuAppDeploymentReset', 'true');
        console.log('✅ Production localStorage cleared successfully');
      } catch (error) {
        console.warn('⚠️ Failed to clear production localStorage:', error);
      }
    }

    // Clear demographic data on page refresh to ensure fresh start
    try {
      localStorage.removeItem('icuAppDemographics');
      console.log('🔄 Demographic data cleared on page refresh for fresh start');
    } catch (error) {
      console.warn('⚠️ Failed to clear demographic data:', error);
    }

    // Load recording counts from localStorage with backward compatibility
    try {
      const savedRecordingsCount = localStorage.getItem('icuAppRecordingsCount');
      if (savedRecordingsCount) {
        const parsedCounts = JSON.parse(savedRecordingsCount);
        console.log('📱 Raw localStorage data:', parsedCounts);

        // Check if data is in old nested format (category -> phraseIndex -> count)
        // vs new flat format (category:phrase -> count)
        const isOldFormat = Object.values(parsedCounts).some(value =>
          typeof value === 'object' && value !== null && !Array.isArray(value)
        );

        if (isOldFormat) {
          console.log('🔄 Converting old nested format to new flat format...');
          const convertedCounts = {};

          // Convert from old format: {category: {phraseIndex: count}}
          // to new format: {"category:phrase": count}
          Object.entries(parsedCounts).forEach(([category, phraseData]) => {
            if (typeof phraseData === 'object' && phraseData !== null) {
              Object.entries(phraseData).forEach(([phraseIndex, count]) => {
                // Map phraseIndex to actual phrase text
                const categoryPhrases = phrases[category];
                if (categoryPhrases && categoryPhrases[parseInt(phraseIndex)]) {
                  const phraseText = categoryPhrases[parseInt(phraseIndex)];
                  const phraseKey = `${category}:${phraseText}`;
                  convertedCounts[phraseKey] = count;
                  console.log(`  Converted: ${category}[${phraseIndex}] = ${count} -> ${phraseKey} = ${count}`);
                } else {
                  // Fallback for unknown phrases
                  const phraseKey = `${category}:phrase_${phraseIndex}`;
                  convertedCounts[phraseKey] = count;
                  console.log(`  Converted (fallback): ${category}[${phraseIndex}] = ${count} -> ${phraseKey} = ${count}`);
                }
              });
            }
          });

          console.log('📱 Converted recording counts:', convertedCounts);
          setRecordingsCount(convertedCounts);

          // Save the converted format back to localStorage
          localStorage.setItem('icuAppRecordingsCount', JSON.stringify(convertedCounts));
          console.log('💾 Saved converted format to localStorage');
        } else {
          console.log('📱 Using current flat format');
          setRecordingsCount(parsedCounts);
          console.log('📱 Final loaded recording counts:', parsedCounts);
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to load recording counts from localStorage:', error);
    }



    // Check if we should bypass the initial screens
    const urlParams = new URLSearchParams(window.location.search);
    const directAccess = urlParams.get('direct');

    // If direct=recording parameter is present, bypass all setup screens
    if (directAccess === 'recording') {
      console.log('Direct access to recording page detected');

      // Set all required states to bypass screens
      setHasConsent(true);
      setDemographicsCompleted(true);
      setTrainingVideoCompleted(true);

      // Set default demographic info if needed
      if (!demographicInfo) {
        setDemographicInfo({
          age: '30-39',
          gender: 'prefer-not-to-say',
          languageBackground: 'english',
          medicalBackground: 'none'
        });
      }

      // Load phrases from local storage or create default phrases
      const loadedPhrases = getSelectedPhrases();
      if (loadedPhrases && loadedPhrases.length > 0) {
        // Use previously selected phrases
        setSelectedPhrases(loadedPhrases);
        setPhrasesSelected(true);
        setSelectedCategory(loadedPhrases[0].category);
        setCurrentStep('recording');
      } else {
        // Create some default phrases if none exist
        const defaultPhrases = [
          { id: 1, phrase: 'I need water', category: 'Basic Needs', text: 'I need water' },
          { id: 2, phrase: 'I am in pain', category: 'Health', text: 'I am in pain' },
          { id: 3, phrase: 'Call my family', category: 'Communication', text: 'Call my family' }
        ];
        setSelectedPhrases(defaultPhrases);
        setPhrasesSelected(true);
        setSelectedCategory('Basic Needs');
        saveSelectedPhrases(defaultPhrases); // Save to local storage
        setCurrentStep('recording');
      }
    } else if (directAccess === 'training') {
      console.log('Direct access to training page detected');

      // Set required states to access training page
      setHasConsent(true);
      setDemographicsCompleted(true);

      // Set default demographic info if needed
      if (!demographicInfo) {
        setDemographicInfo({
          age: '30-39',
          gender: 'prefer-not-to-say',
          languageBackground: 'english',
          medicalBackground: 'none'
        });
      }

      setCurrentStep('training');
    } else if (directAccess === 'mouth-test') {
      console.log('Direct access to mouth detection test detected');
      setCurrentStep('mouth-test');
    } else {
      // Normal flow - just load phrases if they exist
      const loadedPhrases = getSelectedPhrases();
      if (loadedPhrases && loadedPhrases.length > 0) {
        setSelectedPhrases(loadedPhrases);
        setPhrasesSelected(true);
        setSelectedCategory(loadedPhrases[0].category);
      }
    }
  }, []);



  // Handle notification close
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };
  
  // Handle showing receipt and saving all videos
  const handleShowReceipt = async () => {
    try {
      setUploading(true);
      setNotification({
        open: true,
        message: 'Saving all recordings, please wait...',
        severity: 'info'
      });
      
      // Save all videos from memory to disk
      const savedPaths = await videoStore.saveAllRecordings(demographicInfo);
      console.log(`Successfully saved ${savedPaths.length} recordings to disk`);
      
      setNotification({
        open: true,
        message: `Successfully saved ${savedPaths.length} recordings!`,
        severity: 'success'
      });
    } catch (error) {
      console.error('Error saving recordings:', error);
      setNotification({
        open: true,
        message: 'Error saving recordings. Some videos may not have been saved.',
        severity: 'error'
      });
    } finally {
      setUploading(false);
      setShowReceipt(true);
    }
  };
  
  // Handle consent submission
  const handleConsent = () => {
    setHasConsent(true);
    setCurrentStep('demographics');
    setNotification({
      open: true,
      message: 'Thank you for your consent. Please complete the demographics form.',
      severity: 'success'
    });
  };
  
  // Handle demographic form submission
  const handleDemographicSubmit = (demographicData) => {
    setDemographicInfo(demographicData);
    setDemographicsCompleted(true);
    setCurrentStep('training');

    // Save demographics to localStorage for within-session persistence
    // Note: This data is cleared on page refresh to ensure fresh start
    try {
      localStorage.setItem('icuAppDemographics', JSON.stringify(demographicData));
      console.log('📱 Demographics saved to localStorage for session persistence:', demographicData);
    } catch (error) {
      console.warn('⚠️ Failed to save demographics to localStorage:', error);
    }

    setNotification({
      open: true,
      message: 'Demographics saved successfully. Please watch the training video.',
      severity: 'success'
    });
  };
  
  // Handle back to consent
  const handleBackToConsent = () => {
    setHasConsent(false);
    setCurrentStep('demographics'); // Changed from 'home' since we removed home
  };
  
  // Get list of completed phrases (those with 3 recordings)
  const getCompletedPhrases = () => {
    // Get recordings from videoStore
    const recordings = videoStore.getAllRecordings();
    
    // Count recordings per phrase
    const phraseRecordingCounts = {};
    
    recordings.forEach(recording => {
      const key = `${recording.category}-${recording.phrase}`;
      phraseRecordingCounts[key] = (phraseRecordingCounts[key] || 0) + 1;
    });
    
    // Find phrases with required recordings (fully completed)
    return Object.keys(phraseRecordingCounts)
      .filter(key => phraseRecordingCounts[key] >= RECORDINGS_PER_PHRASE)
      .map(key => {
        const [category, ...phraseParts] = key.split('-');
        return {
          category,
          phrase: phraseParts.join('-') // Rejoin in case phrase contains hyphens
        };
      });
  };

  // Generate reference number for completed session
  const generateSessionReference = useCallback(() => {
    try {
      console.log('🔢 Generating session reference number...');

      const sessionParams = {
        demographicInfo,
        selectedPhrases,
        recordingsCount,
        sessionStats,
        sessionRecordingsCount
      };

      const result = referenceNumberService.createSessionReference(sessionParams);

      if (result.success) {
        setCurrentSessionReference(result.referenceNumber);
        console.log('✅ Session reference generated successfully:', result.referenceNumber);
        console.log('📊 Session data stored:', result.sessionData);

        setNotification({
          open: true,
          message: `Session completed! Reference number: ${result.referenceNumber}`,
          severity: 'success'
        });

        return result.referenceNumber;
      } else {
        console.error('❌ Failed to generate session reference:', result.error);
        setNotification({
          open: true,
          message: 'Warning: Could not generate reference number. Please contact support.',
          severity: 'warning'
        });
        return null;
      }
    } catch (error) {
      console.error('❌ Error in generateSessionReference:', error);
      setNotification({
        open: true,
        message: 'Error generating reference number. Please contact support.',
        severity: 'error'
      });
      return null;
    }
  }, [demographicInfo, selectedPhrases, recordingsCount, sessionStats, sessionRecordingsCount]);

  // Handle restart recording session
  const handleRestartSession = () => {
    // Get list of completed phrases
    const completedPhrases = getCompletedPhrases();

    // Store completed phrases in localStorage to access in PhraseSelector
    localStorage.setItem('icuAppCompletedPhrases', JSON.stringify(completedPhrases));

    // Reset to phrase selection page
    setCurrentStep('phrases');
    setShowCompletionPrompt(false);
    setPhrasesSelected(false); // Reset phrase selection state
    setSelectedPhrases([]); // Clear previously selected phrases

    // Reset recording counters
    setCurrentPhraseIndex(0);
    setCurrentRecordingNumber(0);

    // Clear current session reference for new session
    setCurrentSessionReference(null);

    setNotification({
      open: true,
      message: 'Great! Please select new phrases to record.',
      severity: 'success'
    });
  };
  
  // Handle training video completion
  const handleTrainingVideoComplete = () => {
    setTrainingVideoCompleted(true);
    setCurrentStep('phrases');
    setNotification({
      open: true,
      message: 'Training completed. Please select phrases to record.',
      severity: 'success'
    });
  };
  
  // Handle phrases selection
  const handlePhrasesSelected = (selectedPhrasesList) => {
    setSelectedPhrases(selectedPhrasesList);
    setPhrasesSelected(true);
    setCurrentStep('recording');
    setCurrentPhraseIndex(0);

    if (selectedPhrasesList.length > 0) {
      setSelectedCategory(selectedPhrasesList[0].category);

      // Initialize recording number based on existing recordings
      const firstPhrase = selectedPhrasesList[0];
      const existingRecordings = getCurrentRecordingCountForPhrase(firstPhrase.category, firstPhrase.phrase);
      setCurrentRecordingNumber(existingRecordings); // Start with existing count

      console.log(`Starting with phrase: ${firstPhrase.phrase}`);
      console.log(`Existing recordings: ${existingRecordings}`);
      console.log(`UI will start at: ${existingRecordings}/${RECORDINGS_PER_PHRASE}`);
    }

    setNotification({
      open: true,
      message: `${selectedPhrasesList.length} phrases selected for recording.`,
      severity: 'success'
    });
  };
  
  // Get current phrase
  const currentPhrase = selectedPhrases && selectedPhrases[currentPhraseIndex]
    ? selectedPhrases[currentPhraseIndex].phrase
    : '';

  // Debug current phrase calculation
  useEffect(() => {
    console.log('📝 App.js: currentPhrase calculated as:', currentPhrase);
    console.log('📝 App.js: currentPhraseIndex:', currentPhraseIndex);
    console.log('📝 App.js: selectedPhrases length:', selectedPhrases?.length);
    if (selectedPhrases && selectedPhrases[currentPhraseIndex]) {
      console.log('📝 App.js: Current phrase object:', selectedPhrases[currentPhraseIndex]);
    }
  }, [currentPhrase, currentPhraseIndex, selectedPhrases]);
  
  // Handle category change
  const handleCategoryChange = (categoryValue) => {
    // If categoryValue is empty string, it means we're clearing the selection
    const category = categoryValue === '' ? '' : categoryValue;
    setSelectedCategory(category);
    // Find first phrase of this category
    if (selectedPhrases && category !== '') {
      const firstIndex = selectedPhrases.findIndex(p => p.category === category);
      if (firstIndex !== -1) {
        setCurrentPhraseIndex(firstIndex);
        const phrase = selectedPhrases[firstIndex];
        const existingRecordings = getCurrentRecordingCountForPhrase(phrase.category, phrase.phrase);
        setCurrentRecordingNumber(existingRecordings);
      }
    }
  };
  
  // Get current phrase index in category
  const getCurrentPhraseIndexInCategory = () => {
    if (!selectedPhrases || currentPhraseIndex < 0 || !selectedCategory) return 0;

    const phrasesInCategory = selectedPhrases.filter(p => p.category === selectedCategory);
    const currentPhraseInCategory = selectedPhrases[currentPhraseIndex];

    if (!currentPhraseInCategory || currentPhraseInCategory.category !== selectedCategory) return 0;

    return phrasesInCategory.findIndex(p => p.id === currentPhraseInCategory.id) + 1;
  };

  // Get total phrases for category
  const getTotalPhrasesForCategory = () => {
    if (!selectedPhrases || !selectedCategory) return 0;
    return selectedPhrases.filter(p => p.category === selectedCategory).length;
  };

  // Helper function to get current recording count for a phrase
  const getCurrentRecordingCountForPhrase = useCallback((category, phrase) => {
    const phraseKey = `${category}:${phrase}`;
    return recordingsCount[phraseKey] || 0;
  }, [recordingsCount]);
  
  // Handle previous phrase
  const handlePreviousPhrase = () => {
    if (currentPhraseIndex > 0) {
      const prevPhrase = selectedPhrases[currentPhraseIndex - 1];
      setCurrentPhraseIndex(currentPhraseIndex - 1);

      // Set recording number based on existing recordings for this phrase
      const existingRecordings = getCurrentRecordingCountForPhrase(prevPhrase.category, prevPhrase.phrase);
      setCurrentRecordingNumber(existingRecordings);

      // Update category if needed
      if (prevPhrase && prevPhrase.category !== selectedCategory) {
        setSelectedCategory(prevPhrase.category);
      }
    }
  };
  
  // Handle next phrase - wrapped in useCallback to prevent stale closures
  const handleNextPhrase = useCallback(() => {
    console.log('🚀 === HANDLE NEXT PHRASE CALLED ===');
    console.log('🔍 IMMEDIATE STATE CHECK:');
    console.log('  selectedPhrases:', selectedPhrases);
    console.log('  currentPhraseIndex:', currentPhraseIndex);
    console.log('  recordingsCount:', recordingsCount);

    logAppState('handleNextPhrase START');
    console.trace('🚀 Stack trace for handleNextPhrase call:');

    console.log('  📊 Current state:');
    console.log('    selectedPhrases.length:', selectedPhrases?.length);
    console.log('    currentPhraseIndex:', currentPhraseIndex);
    console.log('    Is last phrase?', currentPhraseIndex >= (selectedPhrases?.length - 1));

    // Check if this is the last phrase (completion case)
    if (!selectedPhrases || currentPhraseIndex >= selectedPhrases.length - 1) {
      console.log('  🏁 COMPLETION CASE: This is the last/only phrase');

      // For single phrase: currentPhraseIndex = 0, selectedPhrases.length = 1, so 0 >= (1-1) = true
      // For multiple phrases: we've reached the last phrase

      console.log('  🔍 Verifying all phrases are completed...');
      const incompletePhrase = selectedPhrases?.find(phrase => {
        const phraseKey = `${phrase.category}:${phrase.phrase}`;
        const count = recordingsCount[phraseKey] || 0;
        const isComplete = count >= RECORDINGS_PER_PHRASE;
        console.log(`    ${phraseKey}: ${count}/${RECORDINGS_PER_PHRASE} ${isComplete ? '✅' : '❌'}`);
        return !isComplete;
      });

      if (incompletePhrase) {
        console.log('  ⚠️ INCOMPLETE PHRASE DETECTED:', incompletePhrase);
        console.log('    Not all phrases have required recordings. Staying in recording mode.');
        setNotification({
          open: true,
          message: `Please complete all selected phrases. "${incompletePhrase.phrase}" needs more recordings.`,
          severity: 'warning'
        });
        return; // Don't show completion page yet
      }

      console.log('  ✅ ALL PHRASES COMPLETED! Showing completion page.');

      // Generate reference number for the completed session
      generateSessionReference();

      // Show the completion prompt when we've reached the end of all selected phrases
      console.log('  🏁 SETTING showCompletionPrompt = true (all phrases completed)');
      setShowCompletionPrompt(true);

    } else {
      // ADVANCEMENT CASE: We have more phrases to go through
      console.log('  📝 ADVANCEMENT CASE: Moving to next phrase');

      const nextPhraseIndex = currentPhraseIndex + 1;
      const nextPhrase = selectedPhrases[nextPhraseIndex];
      const nextPhraseKey = `${nextPhrase.category}:${nextPhrase.phrase}`;

      console.log('    From:', selectedPhrases[currentPhraseIndex]?.phrase);
      console.log('    To:', nextPhrase.phrase);
      console.log('    Category:', nextPhrase.category);
      console.log('    Next phrase key:', nextPhraseKey);

      // Set recording number based on how many recordings already exist for the next phrase
      const existingRecordings = getCurrentRecordingCountForPhrase(nextPhrase.category, nextPhrase.phrase);
      console.log('    Existing recordings for next phrase:', existingRecordings);

      // Update all state atomically to ensure proper synchronization
      console.log('  🔄 Updating state...');
      setCurrentPhraseIndex(nextPhraseIndex);
      setCurrentRecordingNumber(existingRecordings); // Use existing count

      // Update category if needed
      if (nextPhrase && nextPhrase.category !== selectedCategory) {
        console.log('    📂 Category change:', selectedCategory, '→', nextPhrase.category);
        setSelectedCategory(nextPhrase.category);
      }

      console.log('  ✅ State update complete');
      console.log('    New currentPhraseIndex:', nextPhraseIndex);
      console.log('    New currentRecordingNumber:', existingRecordings);
      console.log('    VideoRecorder should receive new phrase:', nextPhrase.phrase);

      // Add success notification for phrase advancement
      setNotification({
        open: true,
        message: `Moving to next phrase: "${nextPhrase.phrase}"`,
        severity: 'info'
      });
    }
  }, [selectedPhrases, currentPhraseIndex, recordingsCount, RECORDINGS_PER_PHRASE, generateSessionReference, getCurrentRecordingCountForPhrase, selectedCategory]);
  
  // Handle video recording
  const handleVideoRecorded = async (savedData, metadata, qualityCheck) => {
    console.log('🎯 === APP: handleVideoRecorded called ===');
    logAppState('handleVideoRecorded START');
    console.log('  📊 Function parameters received:');
    console.log('    savedData:', savedData);
    console.log('    metadata:', metadata);
    console.log('    qualityCheck:', qualityCheck);

    setUploading(true);

    // Check if this is a simulated upload (development mode)
    const isSimulatedUpload = savedData?.simulated || metadata?.simulated;
    console.log('  🎭 Upload type:', isSimulatedUpload ? 'SIMULATED (dev mode)' : 'REAL (production)');

    try {
      // Extract data from the new format
      const phrase = metadata.phrase;
      const category = metadata.category;
      const recordingNumber = metadata.recordingNumber;
      const phraseKey = `${category}:${phrase}`;

      console.log('  🔍 METADATA ANALYSIS:');
      console.log('    metadata.phrase:', phrase);
      console.log('    metadata.category:', category);
      console.log('    metadata.recordingNumber:', recordingNumber);
      console.log('    Generated phraseKey:', phraseKey);
      console.log('    Current app state phrase:', currentPhrase);
      console.log('    Current app state category:', selectedCategory);
      console.log('    Expected phraseKey from app state:', `${selectedCategory}:${currentPhrase}`);

      console.log('🎯 START handleVideoRecorded:', { phrase, category, phraseKey });
      console.log('Extracted data:', { phrase, category, recordingNumber });

      // Note: The video has already been uploaded in the VideoRecorder component
      // via videoStore.saveRecording, so we don't need to upload again here
      console.log('Video already uploaded via VideoRecorder component');
      console.log('Upload was successful, proceeding with UI updates...');



      // Update session stats first (for immediate feedback)
      console.log('Updating session stats...');
      setSessionStats(prev => ({
        ...prev,
        recentRecordings: [
          {
            phrase,
            category,
            recordingNumber,
            timestamp: new Date().toISOString()
          },
          ...prev.recentRecordings
        ].slice(0, 10) // Keep only last 10 recordings
      }));

      // Update recording counts and get the new count
      console.log('  🔢 Updating recording counts...');
      console.log('    phraseKey:', phraseKey);
      console.log('    previous recordingsCount:', recordingsCount);

      // Use a variable to track the actual new count
      let actualNewRecordingCount = 0;

      setRecordingsCount(prev => {
        console.log('    📊 Inside setRecordingsCount callback');
        console.log('      prev:', prev);

        // Create a completely new object to ensure React detects the change
        const newCount = JSON.parse(JSON.stringify(prev)); // Deep clone

        if (!newCount[phraseKey]) {
          newCount[phraseKey] = 0;
          console.log('      initialized phraseKey to 0');
        }
        newCount[phraseKey]++;
        actualNewRecordingCount = newCount[phraseKey]; // Capture the actual new count
        console.log('      🔢 RECORDING COUNT UPDATE:');
        console.log('        phraseKey:', phraseKey);
        console.log('        newCount[phraseKey]:', newCount[phraseKey]);
        console.log('        actualNewRecordingCount captured:', actualNewRecordingCount);
        console.log('        RECORDINGS_PER_PHRASE for comparison:', RECORDINGS_PER_PHRASE);
        console.log('        Will trigger auto-advance?', actualNewRecordingCount >= RECORDINGS_PER_PHRASE);
        console.log('        Full newCount object:', newCount);

        // Persist to localStorage for completion tracking
        try {
          localStorage.setItem('icuAppRecordingsCount', JSON.stringify(newCount));
          console.log('      📱 Recording counts saved to localStorage');
        } catch (error) {
          console.warn('      ⚠️ Failed to save recording counts to localStorage:', error);
        }

        return newCount;
      });

      // Update session recording count
      setSessionRecordingsCount(prev => prev + 1);

      // Update current recording number for this phrase
      console.log('  🔢 Setting currentRecordingNumber to:', actualNewRecordingCount);
      setCurrentRecordingNumber(actualNewRecordingCount);

      console.log(`  ✅ Recording ${actualNewRecordingCount}/${RECORDINGS_PER_PHRASE} completed for phrase: ${phrase}`);

      // Show appropriate notification based on upload type
      const notificationMessage = isSimulatedUpload
        ? `Recording ${actualNewRecordingCount}/${RECORDINGS_PER_PHRASE} completed! (Development mode)`
        : `Recording ${actualNewRecordingCount}/${RECORDINGS_PER_PHRASE} uploaded successfully!`;

      setNotification({
        open: true,
        message: notificationMessage,
        severity: 'success'
      });

      // Check for auto-navigation BEFORE async operations to prevent interference
      console.log('  🚦 CRITICAL: Checking auto-navigation condition...');
      console.log('    actualNewRecordingCount:', actualNewRecordingCount);
      console.log('    RECORDINGS_PER_PHRASE:', RECORDINGS_PER_PHRASE);
      console.log('    condition (actualNewRecordingCount >= RECORDINGS_PER_PHRASE):', actualNewRecordingCount >= RECORDINGS_PER_PHRASE);
      console.log('    currentPhraseIndex:', currentPhraseIndex);
      console.log('    selectedPhrases?.length:', selectedPhrases?.length);

      console.log('  🔍 CRITICAL DEBUG: Auto-advancement condition check');
      console.log('    actualNewRecordingCount:', actualNewRecordingCount);
      console.log('    RECORDINGS_PER_PHRASE:', RECORDINGS_PER_PHRASE);
      console.log('    Condition result:', actualNewRecordingCount >= RECORDINGS_PER_PHRASE);

      if (actualNewRecordingCount >= RECORDINGS_PER_PHRASE) {
        console.log(`  🎯 PHRASE COMPLETION DETECTED - ${RECORDINGS_PER_PHRASE} recordings completed`);
        console.log('    Current phrase:', selectedPhrases?.[currentPhraseIndex]?.phrase);
        console.log('    Current phrase index:', currentPhraseIndex);
        console.log('    Total selected phrases:', selectedPhrases?.length);
        console.log('    Is last phrase?', currentPhraseIndex >= (selectedPhrases?.length - 1));
        console.log('    Auto-advancement will be handled by useEffect watching recordingsCount');
      } else {
        console.log(`  ⏳ STAYING ON CURRENT PHRASE - Not yet ${RECORDINGS_PER_PHRASE} recordings`);
        console.log('    Need', RECORDINGS_PER_PHRASE - actualNewRecordingCount, 'more recordings for this phrase');
      }

      // Refresh progress data from S3 after successful upload (async, non-blocking)
      console.log('  🔄 Refreshing progress data from S3...');
      refreshProgress().then(() => {
        console.log('  ✅ Progress data refreshed successfully');
      }).catch(error => {
        console.warn('  ⚠️ Failed to refresh progress data:', error);
      });

      logAppState('handleVideoRecorded END');
    } catch (error) {
      console.error('❌ Error processing recording:', error);

      // Still increment local count even if there are other processing errors
      // This ensures phrase progression works based on local state as fallback
      const phrase = metadata?.phrase || 'unknown';
      const category = metadata?.category || 'unknown';
      const phraseKey = `${category}:${phrase}`;

      console.log('🔄 FALLBACK: Incrementing local count despite error');
      let fallbackRecordingCount = 0;

      setRecordingsCount(prev => {
        const newCount = JSON.parse(JSON.stringify(prev));
        if (!newCount[phraseKey]) {
          newCount[phraseKey] = 0;
        }
        newCount[phraseKey]++;
        fallbackRecordingCount = newCount[phraseKey];

        // Persist to localStorage
        try {
          localStorage.setItem('icuAppRecordingsCount', JSON.stringify(newCount));
          console.log('📱 Fallback: Recording counts saved to localStorage');
        } catch (storageError) {
          console.warn('⚠️ Failed to save fallback recording counts:', storageError);
        }

        return newCount;
      });

      // Update current recording number
      setCurrentRecordingNumber(fallbackRecordingCount);

      // Determine error message based on error type
      let errorMessage = 'Recording completed locally, but upload may have failed.';
      if (error.message?.includes('network') || error.message?.includes('Network')) {
        errorMessage = 'Network error during upload. Recording saved locally.';
      } else if (error.message?.includes('S3') || error.message?.includes('AWS')) {
        errorMessage = 'AWS upload error. Recording saved locally.';
      }

      setNotification({
        open: true,
        message: errorMessage,
        severity: 'warning'
      });

      // Still check for phrase advancement based on local count
      if (fallbackRecordingCount >= RECORDINGS_PER_PHRASE) {
        console.log('🎯 FALLBACK: Phrase completion detected, advancing despite upload error');
        setTimeout(() => {
          handleNextPhrase();
        }, 1000); // Slightly longer delay for error case
      }
    } finally {
      setUploading(false);
    }
  };
  
  // Handle navigation
  const handleNavigation = (step) => {
    console.log('handleNavigation called with step:', step);

    switch(step) {
      case 'demographics':
        setCurrentStep('demographics');
        setShowDemographics(true);
        setShowPhraseSelector(false);
        setShowRecording(false);
        setShowProgress(false);
        setShowTrainingVideo(false);
        setShowReceiptGenerator(false);
        break;
      case 'training':
        setCurrentStep('training');
        setShowTrainingVideo(true);
        setShowDemographics(false);
        setShowPhraseSelector(false);
        setShowRecording(false);
        setShowProgress(false);
        setShowReceiptGenerator(false);
        break;
      case 'phrases':
        setCurrentStep('phrases');
        setShowPhraseSelector(true);
        setShowDemographics(false);  
        setShowRecording(false);
        setShowProgress(false);
        setShowTrainingVideo(false);
        setShowReceiptGenerator(false);
        break;
      case 'recording':
        setCurrentStep('recording');
        setShowRecording(true);
        setShowDemographics(false);
        setShowPhraseSelector(false);
        setShowProgress(false);
        setShowTrainingVideo(false);
        setShowReceiptGenerator(false);
        break;
      case 'progress':
        setCurrentStep('progress');
        setShowProgress(true);
        setShowDemographics(false);
        setShowPhraseSelector(false);
        setShowRecording(false);
        setShowTrainingVideo(false);
        setShowReceiptGenerator(false);
        break;


      default:
        console.log('Unknown step:', step);
    }
  };

  // Expose App state to window for debugging
  useEffect(() => {
    window.appState = {
      currentPhraseIndex,
      currentRecordingNumber,
      recordingsCount,
      selectedPhrases,
      currentPhrase,
      selectedCategory
    };
    window.handleNextPhrase = handleNextPhrase;
  }, [currentPhraseIndex, currentRecordingNumber, recordingsCount, selectedPhrases, currentPhrase, selectedCategory, handleNextPhrase]);

  return (
    <div className="App">
      {/* TEST COMPONENT - Remove after debugging */}
      <TestComponent />

      {/* S3 Progress Preloader - starts loading data in background */}
      <S3ProgressPreloader preloadOnMount={true} preloadDelay={3000} />

      <ThemeProvider theme={createTheme()}>
        <CssBaseline />

        {hasConsent && (
          <NavigationMenu
            onNavigate={handleNavigation}
            currentStep={currentStep}
            hasConsent={hasConsent}
            demographicsCompleted={demographicsCompleted}
            phrasesSelected={phrasesSelected}
          />
        )}

        <Container maxWidth="lg" disableGutters={isMobile} sx={{ px: isMobile ? 1 : 2 }}>
          {!hasConsent ? (
            <>
              <ConsentPage onConsent={handleConsent} />
              {/* EnvTest component removed */}
            </>
          ) : currentStep === 'demographics' || (!demographicsCompleted) ? (
            <DemographicForm
              onSubmit={handleDemographicSubmit}
              onBackToConsent={handleBackToConsent}
              currentDemographics={demographicInfo}
            />
          ) : currentStep === 'training' ? (
            <TrainingVideoPage onComplete={handleTrainingVideoComplete} />
          ) : currentStep === 'mouth-test' ? (
            <MouthDetectionTest />
          ) : currentStep === 'phrases' ? (
            <>
              <PhraseSelector onPhrasesSelected={handlePhrasesSelected} />

            </>
          ) : currentStep === 'recording' ? (
            <>
              {/* Progress button removed as per Task 15 requirements */}
              
              {/* Show Completion Prompt, Collection Tracker, or Phrase Selection UI */}
              {showReceipt ? (
                <ReceiptGenerator
                  demographicInfo={demographicInfo}
                  sessionRecordingsCount={sessionRecordingsCount}
                  onClose={() => {
                    setShowReceipt(false);
                    setShowCompletionPrompt(true);
                  }}
                />
              ) : showCompletionPrompt ? (
                (() => {
                  // Check for early exit status from localStorage
                  const completionData = JSON.parse(localStorage.getItem('icuCompletionData') || '{}');
                  const isEarlyExit = completionData.earlyExit === true;

                  return (
                    <Container maxWidth="md" sx={{ pt: 5, textAlign: 'center' }}>
                      <Paper elevation={3} sx={{ p: 4, borderRadius: 2, backgroundColor: '#ffffff' }}>
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="h3" gutterBottom sx={{ color: '#009688', fontWeight: 'bold' }}>
                            {isEarlyExit
                              ? 'Thank you for your partial participation!'
                              : 'Thank you for making a difference!'
                            }
                          </Typography>
                          <Typography variant="h6" paragraph sx={{ mb: 3 }}>
                            {isEarlyExit
                              ? 'Thank you for your partial participation in the ICU Phrase Collection study. Your recordings will help train a mobile app that uses AI lipreading technology to give a voice to those who can\'t due to medical conditions.'
                              : 'Your recordings will help train a mobile app that uses AI lipreading technology to give a voice to those who can\'t due to medical conditions.'
                            }
                          </Typography>
                          <Typography variant="body1" paragraph sx={{ mb: 3 }}>
                            You've completed {sessionRecordingsCount} recordings across {Object.keys(recordingsCount).length} phrases.
                          </Typography>
                      
                      {/* Reference Number */}
                      <Box sx={{ mb: 3, p: 2, backgroundColor: '#e0f2f1', borderRadius: 2, boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.08)' }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#00796b' }}>
                          Your Reference Number
                        </Typography>
                        <Typography variant="h5" sx={{ fontFamily: 'monospace', letterSpacing: 1, color: '#263238' }}>
                          {currentSessionReference || 'Generating...'}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1, color: '#546e7a' }}>
                          Please save this number if you wish to withdraw consent later.
                        </Typography>
                        {currentSessionReference && (
                          <Typography variant="body2" sx={{ mt: 1, color: '#00796b', fontStyle: 'italic' }}>
                            ✅ Reference number generated and session data stored securely
                          </Typography>
                        )}
                      </Box>
                    </Box>
                    
                    {/* Real-Time S3 Progress Information */}
                    <S3ProgressDisplay
                      compact={true}
                      showRefreshButton={true}
                      onProgressUpdate={(data) => {
                        console.log('📊 Progress updated:', data.overall);
                      }}
                    />
                    
                    {/* Share Link */}
                    <Box sx={{ mb: 4, p: 2, backgroundColor: '#e0f2f1', borderRadius: 2, boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.08)' }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#00796b' }}>
                        Help us reach our goal, copy this link to share with friends
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 1, my: 2 }}>
                        <TextField
                          variant="outlined"
                          size="small"
                          fullWidth
                          value="http://icuphrasecollection.com"
                          InputProps={{
                            readOnly: true,
                            endAdornment: (
                              <IconButton size="small" onClick={() => {
                                navigator.clipboard.writeText("http://icuphrasecollection.com");
                                setNotification({
                                  open: true,
                                  message: 'Link copied to clipboard!',
                                  severity: 'success'
                                });
                              }}>
                                <ContentCopyIcon fontSize="small" />
                              </IconButton>
                            ),
                          }}
                        />
                      </Box>
                    </Box>
                    
                    {/* Action Button */}
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                      <Button
                        variant="contained"
                        size="large"
                        onClick={handleRestartSession}
                        startIcon={<RefreshIcon />}
                        sx={{ 
                          fontWeight: 'bold', 
                          py: 3, 
                          px: 8, 
                          fontSize: '1.3rem',
                          boxShadow: 4,
                          bgcolor: '#009688',
                          '&:hover': { bgcolor: '#00796b' },
                          minWidth: '500px',
                          minHeight: '80px'
                        }}
                      >
                        Got time for more? Press here to continue!
                      </Button>
                    </Box>
                  </Paper>
                </Container>
                  );
                })()
              ) : showCollectionTracker ? (
                <CollectionTracker 
                  recordingsCount={recordingsCount} 
                  totalGoal={COLLECTION_GOAL} 
                  sessionCount={sessionRecordingsCount}
                  onGetReceipt={handleShowReceipt}
                />
              ) : (
                <Container maxWidth="md" sx={{ pt: 3, pb: 8 }}>
                  {/* Category Selector for navigation between selected phrases */}
                  <CategorySelector
                    categories={Array.from(new Set(selectedPhrases?.map(p => p.category) || []))}
                    selectedCategory={selectedCategory}
                    onCategoryChange={handleCategoryChange}
                  />

                  {/* Phrase Display Component */}
                  <PhraseDisplay
                    phrase={currentPhrase}
                    categoryName={selectedCategory}
                    currentIndex={getCurrentPhraseIndexInCategory()}
                    totalPhrases={getTotalPhrasesForCategory()}
                    currentRecording={currentRecordingNumber}
                    totalRecordings={RECORDINGS_PER_PHRASE}
                  />

                  {/* Video Recorder Component */}

                  <VideoRecorder
                    onRecordingComplete={handleVideoRecorded}
                    disabled={uploading}
                    phrase={currentPhrase}
                    category={selectedCategory}
                    recordingNumber={currentRecordingNumber}
                    demographics={demographicInfo}
                    onEarlyExit={handleEarlyExitCompletion}
                  />

                  {/* Phrase Navigation Component */}
                  <PhraseNavigation
                    onPrevious={handlePreviousPhrase}
                    onNext={handleNextPhrase}
                    hasPrevious={currentPhraseIndex > 0}
                    hasNext={selectedPhrases && currentPhraseIndex < selectedPhrases.length - 1}
                    disabled={uploading}
                  />

                  {uploading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                      <CircularProgress size={24} sx={{ mr: 1 }} />
                      <Typography variant="body1">
                        Uploading video...
                      </Typography>
                    </Box>
                  )}
                </Container>
              )}
            </>
          ) : (
            <Typography>Unknown step</Typography>
          )}
        </Container>

        {/* Fixed Recording Progress Bar at Bottom - Only show during recording */}
        {(() => {
          console.log('🎯 App.js: Progress bar condition check:');
          console.log('  currentStep:', currentStep);
          console.log('  showReceipt:', showReceipt);
          console.log('  showCompletionPrompt:', showCompletionPrompt);
          console.log('  showCollectionTracker:', showCollectionTracker);
          console.log('  Should show progress bar:', currentStep === 'recording' && !showReceipt && !showCompletionPrompt && !showCollectionTracker);
          return currentStep === 'recording' && !showReceipt && !showCompletionPrompt && !showCollectionTracker;
        })() && (() => {
          // Debug data being passed to RecordingProgress
          const categories = Array.from(new Set(selectedPhrases?.map(p => p.category) || []));
          const phrases = selectedPhrases?.reduce((acc, p) => {
            acc[p.category] = acc[p.category] || [];
            acc[p.category].push(p.phrase);
            return acc;
          }, {});

          console.log('🎯 App.js: Passing data to RecordingProgress:');
          console.log('  categories:', categories);
          console.log('  phrases:', phrases);
          console.log('  recordingsCount:', recordingsCount);
          console.log('  selectedPhrases:', selectedPhrases);

          return (
            <Box
              sx={{
                position: 'fixed',
                bottom: 0,
                left: 0,
                right: 0,
                bgcolor: 'background.paper',
                borderTop: '1px solid',
                borderColor: 'divider',
                boxShadow: '0 -2px 8px rgba(0,0,0,0.1)',
                zIndex: 1000,
                p: 2
              }}
            >
              <Container maxWidth="md">
                <RecordingProgress
                  categories={categories}
                  phrases={phrases}
                  recordings={recordingsCount}
                />
              </Container>
            </Box>
          );
        })()}




        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
        >
          <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
            {notification.message}
          </Alert>
        </Snackbar>
      </ThemeProvider>
    </div>
  );
};

export default App;
